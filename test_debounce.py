import pandas as pd
from datetime import datetime, timedelta

def detect_product_switches(group, debounce_count=10):
    """
    检测料号切换，返回每个料号投产轮次的首片和末片信息
    增加消抖功能：连续debounce_count片都是非本料号玻璃时，才认为料号切换
    """
    if len(group) == 0:
        return pd.DataFrame()

    # 按时间排序
    group = group.sort_values('GLASS_START_TIME').reset_index(drop=True)

    # 应用消抖逻辑，为每个位置确定"有效料号"
    effective_products = []
    processed = [False] * len(group)

    i = 0
    while i < len(group):
        if processed[i]:
            i += 1
            continue

        current_product = group.iloc[i]['PRODUCT_ID']

        # 找到当前料号的所有相关位置（包括穿插的）
        product_positions = []
        consecutive_other_count = 0

        for j in range(i, len(group)):
            if group.iloc[j]['PRODUCT_ID'] == current_product:
                product_positions.append(j)
                consecutive_other_count = 0
            else:
                consecutive_other_count += 1
                # 如果连续debounce_count片都不是当前料号，停止搜索
                if consecutive_other_count >= debounce_count:
                    break

        # 将这些位置标记为当前料号
        for pos in product_positions:
            if pos < len(effective_products):
                effective_products[pos] = current_product
            else:
                while len(effective_products) <= pos:
                    effective_products.append(None)
                effective_products[pos] = current_product
            processed[pos] = True

        # 移动到下一个未处理的位置
        i += 1
        while i < len(group) and processed[i]:
            i += 1

    # 填充未处理的位置（使用前一个有效料号）
    last_valid_product = None
    for i in range(len(group)):
        if i < len(effective_products) and effective_products[i] is not None:
            last_valid_product = effective_products[i]
        else:
            if i >= len(effective_products):
                effective_products.append(last_valid_product)
            else:
                effective_products[i] = last_valid_product

    # 基于有效料号序列检测切换点
    switches = []
    if not effective_products:
        return pd.DataFrame()

    current_product = effective_products[0]
    start_idx = 0

    for i in range(1, len(effective_products)):
        if effective_products[i] != current_product:
            # 发现切换点
            end_idx = i - 1
            first_row = group.iloc[start_idx]
            last_row = group.iloc[end_idx]

            switches.append({
                'LINE_ID': first_row['LINE_ID'],
                'STEP_ID': first_row['STEP_ID'],
                'PRODUCT_ID': current_product,
                'FIRST_GLASS_TIME': first_row['GLASS_START_TIME'],
                'FIRST_GLASS_ID': first_row['GLASS_ID'],
                'LAST_GLASS_TIME': last_row['GLASS_START_TIME'],
                'LAST_GLASS_ID': last_row['GLASS_ID'],
                'DESCRIPTION': last_row.get('DESCRIPTION', ''),
                'PRODUCTIONTYPE': last_row.get('PRODUCTIONTYPE', '')
            })

            current_product = effective_products[i]
            start_idx = i

    # 处理最后一个料号
    if len(effective_products) > 0:
        end_idx = len(effective_products) - 1
        first_row = group.iloc[start_idx]
        last_row = group.iloc[end_idx]

        switches.append({
            'LINE_ID': first_row['LINE_ID'],
            'STEP_ID': first_row['STEP_ID'],
            'PRODUCT_ID': current_product,
            'FIRST_GLASS_TIME': first_row['GLASS_START_TIME'],
            'FIRST_GLASS_ID': first_row['GLASS_ID'],
            'LAST_GLASS_TIME': last_row['GLASS_START_TIME'],
            'LAST_GLASS_ID': last_row['GLASS_ID'],
            'DESCRIPTION': last_row.get('DESCRIPTION', ''),
            'PRODUCTIONTYPE': last_row.get('PRODUCTIONTYPE', '')
        })

    return pd.DataFrame(switches)

# 创建测试数据
# 模拟场景：A料号 -> B料号穿插 -> A料号穿插 -> B料号连续10片以上
test_data = []
base_time = datetime(2025, 1, 1, 10, 0, 0)

# A料号连续5片
for i in range(5):
    test_data.append({
        'LINE_ID': 'PHT01',
        'STEP_ID': 'BM',
        'PRODUCT_ID': 'A001',
        'GLASS_START_TIME': base_time + timedelta(minutes=i),
        'GLASS_ID': f'A{i+1:03d}',
        'DESCRIPTION': 'Product A',
        'PRODUCTIONTYPE': 'Engineering'
    })

# B料号3片（穿插）
for i in range(3):
    test_data.append({
        'LINE_ID': 'PHT01',
        'STEP_ID': 'BM',
        'PRODUCT_ID': 'B001',
        'GLASS_START_TIME': base_time + timedelta(minutes=5+i),
        'GLASS_ID': f'B{i+1:03d}',
        'DESCRIPTION': 'Product B',
        'PRODUCTIONTYPE': 'Engineering'
    })

# A料号2片（穿插回来）
for i in range(2):
    test_data.append({
        'LINE_ID': 'PHT01',
        'STEP_ID': 'BM',
        'PRODUCT_ID': 'A001',
        'GLASS_START_TIME': base_time + timedelta(minutes=8+i),
        'GLASS_ID': f'A{i+6:03d}',
        'DESCRIPTION': 'Product A',
        'PRODUCTIONTYPE': 'Engineering'
    })

# B料号连续15片（真正切换）
for i in range(15):
    test_data.append({
        'LINE_ID': 'PHT01',
        'STEP_ID': 'BM',
        'PRODUCT_ID': 'B001',
        'GLASS_START_TIME': base_time + timedelta(minutes=10+i),
        'GLASS_ID': f'B{i+4:03d}',
        'DESCRIPTION': 'Product B',
        'PRODUCTIONTYPE': 'Engineering'
    })

# 创建DataFrame
df_test = pd.DataFrame(test_data)

print("原始数据序列:")
print(df_test[['PRODUCT_ID', 'GLASS_ID', 'GLASS_START_TIME']].to_string())

# 测试消抖功能
result = detect_product_switches(df_test, debounce_count=10)

print("\n消抖后的结果:")
print(result[['PRODUCT_ID', 'FIRST_GLASS_ID', 'LAST_GLASS_ID', 'FIRST_GLASS_TIME', 'LAST_GLASS_TIME']].to_string())

print(f"\n预期结果：A001应该从A001到A007（包含穿插），B001应该从B001到B018")
