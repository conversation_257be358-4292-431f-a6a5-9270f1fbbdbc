import pandas as pd

# 转换两个数据源为 DataFrame
try:
    df = pd.DataFrame(sourse_df)
    ld_df = pd.DataFrame(LD_Time)
    platform = 'spotfire'
except:
    df = pd.read_csv("ULD时间.csv", low_memory=False)
    platform = 'ide'

# 确保 GLASS_START_TIME 是 datetime 类型
df['GLASS_START_TIME'] = pd.to_datetime(df['GLASS_START_TIME'])
# ld_df['GLASS_START_TIME'] = pd.to_datetime(ld_df['GLASS_START_TIME'])

# 添加制程映射
step_mapping = {
    '202000': 'BM',
    '203000': 'R',
    '204000': 'G',
    '205000': 'B',
    '206000': 'OC',
    '207000': 'PS'
}

# 按照 LINE_ID、PRODUCT_ID 和 STEP_ID 分组，并找出 GLASS_START_TIME 最大的那一行
df = df.loc[df.groupby(['LINE_ID', 'PRODUCT_ID'])['GLASS_START_TIME'].idxmax()]

# 将 STEP_ID 转换为制程名称
df['STEP_ID'] = df['STEP_ID'].replace(step_mapping)

# 只保留需要的列
df = df[['LINE_ID', 'STEP_ID', 'PRODUCT_ID', 'GLASS_START_TIME', 'GLASS_ID', 'DESCRIPTION', 'PRODUCTIONTYPE']]

# 按照 LINE_ID 升序和 GLASS_START_TIME 升序排序
df = df.sort_values(by=['LINE_ID', 'GLASS_START_TIME']).reset_index(drop=True)

#筛出PHT线
df = df[df['LINE_ID'].str.contains('PHT')]

# 为 result 创建副本并重命名
result = df.rename(columns={
    'LINE_ID': '线体',
    'STEP_ID': '制程',
    'PRODUCT_ID': '产品料号',
    'GLASS_START_TIME': 'ULD最后流片时间',
    'GLASS_ID': '最后一片Glass ID',
    'DESCRIPTION': '产品描述',
    'PRODUCTIONTYPE': '产品类型',
})

# 为 time_agg 处理数据
df_time = df.copy()
df_time['START_TIME'] = df_time.groupby(['LINE_ID'])['GLASS_START_TIME'].shift(1)
df_time['END_TIME'] = df_time['GLASS_START_TIME']
time_agg = df_time.groupby(['LINE_ID'], group_keys=False).apply(lambda group: group.iloc[1:]).reset_index(drop=True)

# 计算耗时（小时）
time_agg['DURATION'] = (time_agg['END_TIME'] - time_agg['START_TIME']).dt.total_seconds() / 3600
time_agg['DURATION'] = time_agg['DURATION'].round(1)

# 删除'CF料号'
time_agg['DESCRIPTION'] = (
    time_agg['DESCRIPTION']
    .str.strip('CF料号')
    .str.strip('料号')
    .str.strip()
)

# 获取两个数据源中每个LINE_ID和STEP_ID的最后一个PRODUCT_ID
source_last_products = df.groupby(['LINE_ID'])['PRODUCT_ID'].last()

if platform == 'spotfire':
    ld_df['STEP_ID'] = ld_df['STEP_ID'].map(step_mapping)
    ld_df = ld_df.loc[ld_df.groupby(['LINE_ID', 'PRODUCT_ID'])['GLASS_START_TIME'].idxmax()]
    ld_last_products = ld_df.groupby(['LINE_ID'])['PRODUCT_ID'].last()

# 默认所有状态为"已Run完"
time_agg['STATUS'] = '已Run完'

# 获取每个产线和制程的最后一个产品记录的索引
last_records = df.groupby(['LINE_ID'])['GLASS_START_TIME'].idxmax()

# 获取这些最后记录的PRODUCT_ID
last_products = df.loc[last_records, ['LINE_ID', 'PRODUCT_ID']]

# 将对应的最后一条记录状态改为"Run货中"
for _, row in last_products.iterrows():
    idx = time_agg[
        (time_agg['LINE_ID'] == row['LINE_ID']) &
        (time_agg['PRODUCT_ID'] == row['PRODUCT_ID'])
    ].index
    if len(idx) > 0:
        time_agg.loc[idx[-1], 'STATUS'] = 'Run货中'

# 筛出新品
time_agg = time_agg[time_agg['PRODUCTIONTYPE'] == 'Engineering']

# 只保留需要的列
time_agg = time_agg[['LINE_ID', 'STEP_ID', 'PRODUCT_ID', 'DESCRIPTION', 'STATUS', 'DURATION', 'START_TIME', 'END_TIME']]

# 重命名列
time_agg.rename(columns={
    'LINE_ID': '线体',
    'STEP_ID': '制程',
    'PRODUCT_ID': '产品料号',
    'DURATION': '当前总耗时(h)',
    'START_TIME': '前产品ULD时间',
    'END_TIME': '本产品ULD时间',
    'DESCRIPTION': '产品描述',
    'STATUS': '状态',
}, inplace=True)

if platform == 'ide':
    result.to_excel('result.xlsx')
    time_agg.to_excel('time_agg.xlsx')