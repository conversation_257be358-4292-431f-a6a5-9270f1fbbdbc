import pandas as pd

# 转换两个数据源为 DataFrame
try:
    df_raw = pd.DataFrame(sourse_df)
    ld_df = pd.DataFrame(LD_Time)
    platform = 'spotfire'
except:
    df_raw = pd.read_csv("ULD时间.csv", low_memory=False)
    platform = 'ide'

# 确保 GLASS_START_TIME 是 datetime 类型
df_raw['GLASS_START_TIME'] = pd.to_datetime(df_raw['GLASS_START_TIME'])
# ld_df['GLASS_START_TIME'] = pd.to_datetime(ld_df['GLASS_START_TIME'])

# 添加制程映射
step_mapping = {
    '202000': 'BM',
    '203000': 'R',
    '204000': 'G',
    '205000': 'B',
    '206000': 'OC',
    '207000': 'PS'
}

# 将 STEP_ID 转换为制程名称
df_raw['STEP_ID'] = df_raw['STEP_ID'].replace(step_mapping)

# 检查必需的列是否存在，如果不存在则创建空列
required_columns = ['LINE_ID', 'STEP_ID', 'PRODUCT_ID', 'GLASS_START_TIME', 'GLASS_ID', 'DESCRIPTION', 'PRODUCTIONTYPE']
for col in required_columns:
    if col not in df_raw.columns:
        df_raw[col] = ''

# 只保留需要的列
df_raw = df_raw[required_columns]

#筛出PHT线
df_raw = df_raw[df_raw['LINE_ID'].str.contains('PHT')]

# 按照 LINE_ID、STEP_ID 和 GLASS_START_TIME 升序排序
df_raw = df_raw.sort_values(by=['LINE_ID', 'STEP_ID', 'GLASS_START_TIME']).reset_index(drop=True)

def detect_product_switches(group, debounce_count=10):
    """
    检测料号切换，返回每个料号投产轮次的首片和末片信息
    增加消抖功能：连续debounce_count片都是非本料号玻璃时，才认为料号切换
    """
    if len(group) == 0:
        return pd.DataFrame()

    # 按时间排序
    group = group.sort_values('GLASS_START_TIME').reset_index(drop=True)

    # 应用消抖逻辑，找到真正的料号切换点
    debounced_products = []
    i = 0

    while i < len(group):
        current_product = group.iloc[i]['PRODUCT_ID']
        start_idx = i

        # 找到当前料号的所有玻璃（包括穿插的）
        j = i + 1
        last_current_product_idx = i
        consecutive_other_count = 0

        while j < len(group):
            if group.iloc[j]['PRODUCT_ID'] == current_product:
                # 遇到相同料号，重置连续计数器
                consecutive_other_count = 0
                last_current_product_idx = j
            else:
                # 遇到不同料号，增加连续计数器
                consecutive_other_count += 1

                # 如果连续10片都不是当前料号，认为当前料号结束
                if consecutive_other_count >= debounce_count:
                    break
            j += 1

        # 记录当前料号的首片和末片信息
        first_row = group.iloc[start_idx]
        last_row = group.iloc[last_current_product_idx]

        debounced_products.append({
            'LINE_ID': first_row['LINE_ID'],
            'STEP_ID': first_row['STEP_ID'],
            'PRODUCT_ID': current_product,
            'FIRST_GLASS_TIME': first_row['GLASS_START_TIME'],
            'FIRST_GLASS_ID': first_row['GLASS_ID'],
            'LAST_GLASS_TIME': last_row['GLASS_START_TIME'],
            'LAST_GLASS_ID': last_row['GLASS_ID'],
            'DESCRIPTION': last_row.get('DESCRIPTION', ''),
            'PRODUCTIONTYPE': last_row.get('PRODUCTIONTYPE', ''),
            'START_IDX': start_idx,
            'END_IDX': last_current_product_idx
        })

        # 移动到下一个不同的料号
        i = last_current_product_idx + 1
        while i < len(group) and group.iloc[i]['PRODUCT_ID'] == current_product:
            i += 1

    return pd.DataFrame(debounced_products)

# 按线体和制程分组，检测料号切换
product_switches_list = []
for (line_id, step_id), group in df_raw.groupby(['LINE_ID', 'STEP_ID']):
    switches_df = detect_product_switches(group)
    if not switches_df.empty:
        product_switches_list.append(switches_df)

# 合并所有切换记录
if product_switches_list:
    df = pd.concat(product_switches_list, ignore_index=True)
else:
    df = pd.DataFrame()

# 按照 LINE_ID、STEP_ID 和 LAST_GLASS_TIME 升序排序
df = df.sort_values(by=['LINE_ID', 'STEP_ID', 'LAST_GLASS_TIME']).reset_index(drop=True)

# 为 result 创建副本并重命名
result = df.rename(columns={
    'LINE_ID': '线体',
    'STEP_ID': '制程',
    'PRODUCT_ID': '产品料号',
    'FIRST_GLASS_TIME': 'ULD首片流片时间',
    'FIRST_GLASS_ID': '首片Glass ID',
    'LAST_GLASS_TIME': 'ULD最后流片时间',
    'LAST_GLASS_ID': '最后一片Glass ID',
    'DESCRIPTION': '产品描述',
    'PRODUCTIONTYPE': '产品类型'
})

# 为 time_agg 处理数据
df_time = df.copy()

# 按线体和制程分组，计算前一个产品的结束时间作为当前产品的开始时间
df_time['START_TIME'] = df_time.groupby(['LINE_ID', 'STEP_ID'])['LAST_GLASS_TIME'].shift(1)
df_time['END_TIME'] = df_time['LAST_GLASS_TIME']

# 移除第一条记录（没有前置产品的记录）
time_agg = df_time.dropna(subset=['START_TIME']).copy()

# 计算当前总耗时（小时）- 从前产品结束到当前产品结束
time_agg['TOTAL_DURATION'] = (time_agg['END_TIME'] - time_agg['START_TIME']).dt.total_seconds() / 3600
time_agg['TOTAL_DURATION'] = time_agg['TOTAL_DURATION'].round(1)

# 计算当前净耗时（小时）- 从当前产品首片到末片
time_agg['NET_DURATION'] = (time_agg['LAST_GLASS_TIME'] - time_agg['FIRST_GLASS_TIME']).dt.total_seconds() / 3600
time_agg['NET_DURATION'] = time_agg['NET_DURATION'].round(1)

# 删除'CF料号'
time_agg['DESCRIPTION'] = (
    time_agg['DESCRIPTION']
    .str.strip('CF料号')
    .str.strip('料号')
    .str.strip()
)

# 获取两个数据源中每个LINE_ID和STEP_ID的最后一个PRODUCT_ID
source_last_products = df.groupby(['LINE_ID', 'STEP_ID'])['PRODUCT_ID'].last()

if platform == 'spotfire':
    ld_df['STEP_ID'] = ld_df['STEP_ID'].map(step_mapping)

    # 检查LD数据的必需列是否存在，如果不存在则创建空列
    for col in required_columns:
        if col not in ld_df.columns:
            ld_df[col] = ''

    # 对LD数据也进行料号切换检测
    ld_switches_list = []
    for (line_id, step_id), group in ld_df.groupby(['LINE_ID', 'STEP_ID']):
        switches_df = detect_product_switches(group)
        if not switches_df.empty:
            ld_switches_list.append(switches_df)

    if ld_switches_list:
        ld_df_processed = pd.concat(ld_switches_list, ignore_index=True)
        ld_last_products = ld_df_processed.groupby(['LINE_ID', 'STEP_ID'])['PRODUCT_ID'].last()
    else:
        ld_last_products = pd.Series(dtype=object)

# 默认所有状态为"已Run完"
time_agg['STATUS'] = '已Run完'

# 获取每个产线和制程的最后一个产品记录的索引
last_records = df.groupby(['LINE_ID', 'STEP_ID'])['LAST_GLASS_TIME'].idxmax()

# 获取这些最后记录的LINE_ID, STEP_ID, PRODUCT_ID
last_products = df.loc[last_records, ['LINE_ID', 'STEP_ID', 'PRODUCT_ID']]

# 将对应的最后一条记录状态改为"Run货中"
for _, row in last_products.iterrows():
    idx = time_agg[
        (time_agg['LINE_ID'] == row['LINE_ID']) &
        (time_agg['STEP_ID'] == row['STEP_ID']) &
        (time_agg['PRODUCT_ID'] == row['PRODUCT_ID'])
    ].index
    if len(idx) > 0:
        time_agg.loc[idx[-1], 'STATUS'] = 'Run货中'

# 筛出新品
time_agg = time_agg[time_agg['PRODUCTIONTYPE'] == 'Engineering']

# 只保留需要的列
time_agg = time_agg[['LINE_ID', 'STEP_ID', 'PRODUCT_ID', 'DESCRIPTION', 'STATUS', 'TOTAL_DURATION', 'NET_DURATION', 'START_TIME', 'END_TIME', 'FIRST_GLASS_TIME']]

# 重命名列
time_agg.rename(columns={
    'LINE_ID': '线体',
    'STEP_ID': '制程',
    'PRODUCT_ID': '产品料号',
    'TOTAL_DURATION': '当前总耗时(h)',
    'NET_DURATION': '当前净耗时(h)',
    'START_TIME': '前产品ULD时间',
    'END_TIME': '本产品ULD时间',
    'FIRST_GLASS_TIME': '本产品首片时间',
    'DESCRIPTION': '产品描述',
    'STATUS': '状态',
}, inplace=True)

if platform == 'ide':
    result.to_excel('result.xlsx')
    time_agg.to_excel('time_agg.xlsx')