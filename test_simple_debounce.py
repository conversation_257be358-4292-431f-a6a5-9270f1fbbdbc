import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>

def detect_product_switches_simple(group, debounce_count=10):
    """
    简化版消抖逻辑测试
    """
    if len(group) == 0:
        return pd.DataFrame()
    
    # 按时间排序
    group = group.sort_values('GLASS_START_TIME').reset_index(drop=True)
    
    print("原始序列:")
    for i, row in group.iterrows():
        print(f"  {i}: {row['PRODUCT_ID']} - {row['GLASS_ID']}")
    
    # 为每片玻璃确定其有效归属料号
    effective_products = []
    
    for i in range(len(group)):
        current_product = group.iloc[i]['PRODUCT_ID']
        
        # 检查从当前位置开始，是否有连续debounce_count片非当前料号
        consecutive_other_count = 0
        should_end_here = False
        
        for j in range(i + 1, len(group)):
            if group.iloc[j]['PRODUCT_ID'] != current_product:
                consecutive_other_count += 1
                if consecutive_other_count >= debounce_count:
                    should_end_here = True
                    break
            else:
                consecutive_other_count = 0
        
        # 如果应该在这里结束，或者已经是最后一片，则归属于当前料号
        # 否则，需要向前查找应该归属的料号
        if should_end_here or i == len(group) - 1:
            effective_products.append(current_product)
            print(f"  位置{i}: {current_product} -> {current_product} (结束点或最后一片)")
        else:
            # 向前查找最近的"稳定"料号
            found_stable_product = current_product
            for k in range(i - 1, -1, -1):
                if k < len(effective_products):
                    found_stable_product = effective_products[k]
                    break
            effective_products.append(found_stable_product)
            print(f"  位置{i}: {current_product} -> {found_stable_product} (继承)")
    
    print("\n有效料号序列:")
    for i, prod in enumerate(effective_products):
        print(f"  {i}: {prod}")
    
    # 基于有效料号序列检测切换点
    switches = []
    if not effective_products:
        return pd.DataFrame()
    
    current_product = effective_products[0]
    start_idx = 0
    
    for i in range(1, len(effective_products)):
        if effective_products[i] != current_product:
            # 发现切换点
            end_idx = i - 1
            first_row = group.iloc[start_idx]
            last_row = group.iloc[end_idx]
            
            print(f"\n切换点: {current_product} 从位置{start_idx}到{end_idx}")
            
            switches.append({
                'PRODUCT_ID': current_product,
                'FIRST_GLASS_ID': first_row['GLASS_ID'],
                'LAST_GLASS_ID': last_row['GLASS_ID'],
                'FIRST_GLASS_TIME': first_row['GLASS_START_TIME'],
                'LAST_GLASS_TIME': last_row['GLASS_START_TIME']
            })
            
            current_product = effective_products[i]
            start_idx = i
    
    # 处理最后一个料号
    if len(effective_products) > 0:
        end_idx = len(effective_products) - 1
        first_row = group.iloc[start_idx]
        last_row = group.iloc[end_idx]
        
        print(f"\n最后料号: {current_product} 从位置{start_idx}到{end_idx}")
        
        switches.append({
            'PRODUCT_ID': current_product,
            'FIRST_GLASS_ID': first_row['GLASS_ID'],
            'LAST_GLASS_ID': last_row['GLASS_ID'],
            'FIRST_GLASS_TIME': first_row['GLASS_START_TIME'],
            'LAST_GLASS_TIME': last_row['GLASS_START_TIME']
        })
    
    return pd.DataFrame(switches)

# 创建简单测试数据
test_data = []
base_time = datetime(2025, 1, 1, 10, 0, 0)

# A料号5片
for i in range(5):
    test_data.append({
        'PRODUCT_ID': 'A001',
        'GLASS_START_TIME': base_time + timedelta(minutes=i),
        'GLASS_ID': f'A{i+1:03d}'
    })

# B料号3片（穿插）
for i in range(3):
    test_data.append({
        'PRODUCT_ID': 'B001',
        'GLASS_START_TIME': base_time + timedelta(minutes=5+i),
        'GLASS_ID': f'B{i+1:03d}'
    })

# A料号2片（穿插回来）
for i in range(2):
    test_data.append({
        'PRODUCT_ID': 'A001',
        'GLASS_START_TIME': base_time + timedelta(minutes=8+i),
        'GLASS_ID': f'A{i+6:03d}'
    })

# B料号连续15片（真正切换）
for i in range(15):
    test_data.append({
        'PRODUCT_ID': 'B001',
        'GLASS_START_TIME': base_time + timedelta(minutes=10+i),
        'GLASS_ID': f'B{i+4:03d}'
    })

# 创建DataFrame
df_test = pd.DataFrame(test_data)

print("测试消抖逻辑 (debounce_count=10):")
result = detect_product_switches_simple(df_test, debounce_count=10)

print("\n最终结果:")
print(result.to_string())

print(f"\n期望结果：")
print(f"A001: A001 -> A007 (包含穿插)")
print(f"B001: B001 -> B018 (包含穿插)")
